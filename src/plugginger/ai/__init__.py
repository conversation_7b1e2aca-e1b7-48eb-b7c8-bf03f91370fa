"""
AI-powered plugin generation for Plugginger framework.

This module provides intelligent plugin generation capabilities using Large Language Models
with structured output validation and wiring analysis.

Key Components:
- LLM Provider abstraction for different AI services
- EBNF grammar system for structured JSON outputs
- Wiring analysis for context-aware plugin generation
- Validation pipeline for generated plugins

Environment Variables:
- PLUGGINGER_LLM_PROVIDER: LLM provider (openai, anthropic, local)
- PLUGGINGER_LLM_API_KEY: API key for the LLM provider
- PLUGGINGER_LLM_MODEL: Model name (optional, uses provider defaults)
- PLUGGINGER_LLM_BASE_URL: Base URL for local/custom providers (optional)

Example:
    >>> from plugginger.ai import PluginGenerator
    >>> generator = PluginGenerator()
    >>> plugin_spec = await generator.generate_plugin(
    ...     prompt="Email service with authentication",
    ...     context_path="./my_app"
    ... )
"""

from plugginger.ai.llm_provider import LL<PERSON>rovider, LLMProviderFactory
from plugginger.ai.plugin_generator import PluginGenerator
from plugginger.ai.types import PluginGenerationRequest, PluginGenerationResult

__all__ = [
    "LLMProvider",
    "LLMProviderFactory",
    "PluginGenerator",
    "PluginGenerationRequest",
    "PluginGenerationResult",
]
