"""
LLM Response Validation Service.

Provides validation of LLM responses against expected formats and schemas.
"""

import json
import logging
from typing import Any

logger = logging.getLogger(__name__)


class ResponseValidationService:
    """Service for validating LLM responses."""

    def __init__(self) -> None:
        """Initialize validation service."""
        self.logger = logger

    async def validate_json_response(
        self,
        response_content: str,
        expected_schema: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Validate LLM response as JSON.

        Args:
            response_content: Raw response content from LLM
            expected_schema: Optional JSON schema for validation

        Returns:
            Validation result with parsed data or errors
        """
        self.logger.debug("Validating JSON response")

        try:
            # Parse JSON
            parsed_data = json.loads(response_content)

            result = {
                "valid": True,
                "data": parsed_data,
                "errors": [],
                "format": "json"
            }

            # Schema validation if provided
            if expected_schema:
                schema_validation = await self._validate_against_schema(parsed_data, expected_schema)
                result.update(schema_validation)

            return result

        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "data": None,
                "errors": [f"Invalid JSON: {e}"],
                "format": "json",
                "raw_content": response_content
            }

    async def validate_ebnf_response(
        self,
        response_content: str,
        ebnf_grammar: str
    ) -> dict[str, Any]:
        """Validate LLM response against EBNF grammar.

        Args:
            response_content: Raw response content from LLM
            ebnf_grammar: EBNF grammar string

        Returns:
            Validation result with grammar compliance
        """
        self.logger.debug("Validating EBNF response")

        # TODO: Implement actual EBNF validation
        # For now, try JSON parsing as a basic check
        try:
            parsed_data = json.loads(response_content)
            return {
                "valid": True,
                "data": parsed_data,
                "errors": [],
                "format": "ebnf",
                "grammar": ebnf_grammar,
                "grammar_compliant": True
            }
        except json.JSONDecodeError:
            return {
                "valid": False,
                "data": None,
                "errors": ["Response does not match EBNF grammar"],
                "format": "ebnf",
                "grammar": ebnf_grammar,
                "grammar_compliant": False,
                "raw_content": response_content
            }

    async def validate_text_response(
        self,
        response_content: str,
        expected_patterns: list[str] | None = None,
        min_length: int | None = None,
        max_length: int | None = None
    ) -> dict[str, Any]:
        """Validate plain text LLM response.

        Args:
            response_content: Raw response content from LLM
            expected_patterns: Optional regex patterns to match
            min_length: Minimum expected length
            max_length: Maximum expected length

        Returns:
            Validation result for text response
        """
        self.logger.debug("Validating text response")

        errors = []
        warnings = []

        # Length validation
        content_length = len(response_content)
        if min_length and content_length < min_length:
            errors.append(f"Response too short: {content_length} < {min_length}")
        if max_length and content_length > max_length:
            warnings.append(f"Response too long: {content_length} > {max_length}")

        # Pattern validation
        pattern_matches = []
        if expected_patterns:
            import re
            for pattern in expected_patterns:
                try:
                    if re.search(pattern, response_content):
                        pattern_matches.append(pattern)
                except re.error as e:
                    errors.append(f"Invalid regex pattern '{pattern}': {e}")

        return {
            "valid": len(errors) == 0,
            "data": response_content,
            "errors": errors,
            "warnings": warnings,
            "format": "text",
            "length": content_length,
            "pattern_matches": pattern_matches
        }

    async def extract_json_from_text(
        self,
        response_content: str
    ) -> dict[str, Any]:
        """Extract JSON from mixed text response.

        Args:
            response_content: Raw response content that may contain JSON

        Returns:
            Extraction result with found JSON data
        """
        self.logger.debug("Extracting JSON from text response")

        import re

        # Try to find JSON blocks in the text
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # JSON in code blocks
            r'```\s*(\{.*?\})\s*```',      # JSON in generic code blocks
            r'(\{[^{}]*\})',               # Simple JSON objects
            r'(\[[^\[\]]*\])'              # Simple JSON arrays
        ]

        extracted_json = []
        for pattern in json_patterns:
            matches = re.findall(pattern, response_content, re.DOTALL)
            for match in matches:
                try:
                    parsed = json.loads(match)
                    extracted_json.append({
                        "data": parsed,
                        "raw": match,
                        "valid": True
                    })
                except json.JSONDecodeError:
                    extracted_json.append({
                        "data": None,
                        "raw": match,
                        "valid": False
                    })

        return {
            "found_json": len(extracted_json) > 0,
            "json_blocks": extracted_json,
            "valid_json_count": sum(1 for item in extracted_json if item["valid"]),
            "raw_content": response_content
        }

    async def _validate_against_schema(
        self,
        data: Any,
        schema: dict[str, Any]
    ) -> dict[str, Any]:
        """Validate data against JSON schema.

        Args:
            data: Parsed JSON data
            schema: JSON schema dictionary

        Returns:
            Schema validation result
        """
        # TODO: Implement proper JSON schema validation
        # For now, just check basic structure
        errors = []

        if schema.get("type") == "object" and not isinstance(data, dict):
            errors.append("Expected object, got " + type(data).__name__)
        elif schema.get("type") == "array" and not isinstance(data, list):
            errors.append("Expected array, got " + type(data).__name__)

        # Check required properties for objects
        if isinstance(data, dict) and "required" in schema:
            for required_field in schema["required"]:
                if required_field not in data:
                    errors.append(f"Missing required field: {required_field}")

        return {
            "schema_valid": len(errors) == 0,
            "schema_errors": errors
        }

    async def validate_response_format(
        self,
        response_content: str,
        expected_format: str,
        validation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Validate response against expected format.

        Args:
            response_content: Raw response content from LLM
            expected_format: Expected format (json, ebnf, text)
            validation_options: Format-specific validation options

        Returns:
            Format validation result
        """
        options = validation_options or {}

        if expected_format.lower() == "json":
            return await self.validate_json_response(
                response_content,
                options.get("schema")
            )
        elif expected_format.lower() == "ebnf":
            return await self.validate_ebnf_response(
                response_content,
                options.get("grammar", "")
            )
        elif expected_format.lower() == "text":
            return await self.validate_text_response(
                response_content,
                options.get("patterns"),
                options.get("min_length"),
                options.get("max_length")
            )
        else:
            return {
                "valid": False,
                "errors": [f"Unsupported format: {expected_format}"],
                "format": expected_format
            }

    async def suggest_fixes(
        self,
        validation_result: dict[str, Any]
    ) -> list[str]:
        """Suggest fixes for validation errors.

        Args:
            validation_result: Result from validation method

        Returns:
            List of suggested fixes
        """
        suggestions = []

        if not validation_result.get("valid", True):
            errors = validation_result.get("errors", [])

            for error in errors:
                if "Invalid JSON" in error:
                    suggestions.append("Check for missing quotes, commas, or brackets in JSON")
                elif "too short" in error:
                    suggestions.append("Provide more detailed response content")
                elif "too long" in error:
                    suggestions.append("Reduce response length or increase max_length limit")
                elif "Missing required field" in error:
                    suggestions.append("Ensure all required fields are included in response")
                elif "EBNF grammar" in error:
                    suggestions.append("Review EBNF grammar constraints and adjust response format")

        return suggestions
