"""
Integration tests for CLI with real LLM calls.

These tests verify that the CLI works end-to-end with real LLM providers.
"""

import os
import subprocess
import tempfile
from pathlib import Path

import pytest


class TestCLIIntegration:
    """Integration tests for CLI with real LLM providers."""

    def _run_cli_command(self, args: list[str], env_vars: dict[str, str] | None = None) -> subprocess.CompletedProcess[str]:
        """Run a CLI command with optional environment variables."""
        env = dict(os.environ)
        if env_vars:
            env.update(env_vars)

        return subprocess.run(
            ["python", "-m", "plugginger.cli"] + args,
            cwd="/home/<USER>/Python/plugginger",
            capture_output=True,
            text=True,
            timeout=60,
            env=env
        )

    def test_cli_help_model_selection(self) -> None:
        """Test that CLI help shows model selection options."""
        result = self._run_cli_command(["new", "plugin", "--help"])

        assert result.returncode == 0
        help_text = result.stdout

        # Check for model selection options
        expected_options = [
            "--model-tier",
            "--max-cost",
            "--max-response-time",
            "--preferred-provider",
            "--model-selection"
        ]

        for option in expected_options:
            assert option in help_text, f"Option {option} not found in CLI help"

    @pytest.mark.skipif(not os.getenv("GOOGLE_API_KEY"), reason="GOOGLE_API_KEY not set")
    def test_cli_gemini_generation(self) -> None:
        """Test CLI plugin generation with Gemini provider."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self._run_cli_command([
                "new", "plugin", "test_gemini_plugin",
                "--output-dir", temp_dir,
                "--prompt", "Create a simple greeting service",
                "--model-tier", "fast",
                "--preferred-provider", "gemini",
                "--max-cost", "0.001",
                "--no-tests"  # Skip tests for faster execution
            ], env_vars={
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", "")
            })

            # Check if command succeeded
            if result.returncode != 0:
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")

            # Command should succeed (even if it falls back to template generation)
            assert result.returncode == 0

            # Check if plugin directory was created
            plugin_path = Path(temp_dir) / "test_gemini_plugin"
            if not plugin_path.exists():
                # Check for underscore version
                plugin_path = Path(temp_dir) / "test_gemini_plugin".replace("-", "_")

            assert plugin_path.exists(), f"Plugin directory not created at {plugin_path}"

            # Check for essential files
            expected_files = ["manifest.yaml"]
            for file_name in expected_files:
                file_path = plugin_path / file_name
                assert file_path.exists(), f"Expected file {file_name} not found"

    @pytest.mark.skipif(not os.getenv("OPENAI_API_KEY"), reason="OPENAI_API_KEY not set")
    def test_cli_openai_generation(self) -> None:
        """Test CLI plugin generation with OpenAI provider."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self._run_cli_command([
                "new", "plugin", "test_openai_plugin",
                "--output-dir", temp_dir,
                "--prompt", "Create a simple data processing service",
                "--model-tier", "balanced",
                "--preferred-provider", "openai",
                "--quality-threshold", "0.6",
                "--no-tests"  # Skip tests for faster execution
            ], env_vars={
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", "")
            })

            # Check if command succeeded
            if result.returncode != 0:
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")

            # Command should succeed (even if it falls back to template generation)
            assert result.returncode == 0

            # Check if plugin directory was created
            plugin_path = Path(temp_dir) / "test_openai_plugin"
            if not plugin_path.exists():
                # Check for underscore version
                plugin_path = Path(temp_dir) / "test_openai_plugin".replace("-", "_")

            assert plugin_path.exists(), f"Plugin directory not created at {plugin_path}"

    def test_cli_ollama_generation(self) -> None:
        """Test CLI plugin generation with Ollama provider (if available)."""
        # Check if Ollama is available
        try:
            import asyncio

            import aiohttp

            async def check_ollama():
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get("http://localhost:11434/api/tags", timeout=aiohttp.ClientTimeout(total=5)) as response:
                            return response.status == 200
                except Exception:
                    return False

            ollama_available = asyncio.run(check_ollama())
            if not ollama_available:
                pytest.skip("Ollama not available")
        except ImportError:
            pytest.skip("aiohttp not available")

        with tempfile.TemporaryDirectory() as temp_dir:
            result = self._run_cli_command([
                "new", "plugin", "test_ollama_plugin",
                "--output-dir", temp_dir,
                "--prompt", "Create a simple hello world service",
                "--model-tier", "fast",
                "--preferred-provider", "ollama",
                "--max-response-time", "5000",
                "--no-tests"  # Skip tests for faster execution
            ])

            # Check if command succeeded
            if result.returncode != 0:
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")

            # Command should succeed (even if it falls back to template generation)
            assert result.returncode == 0

            # Check if plugin directory was created
            plugin_path = Path(temp_dir) / "test_ollama_plugin"
            if not plugin_path.exists():
                # Check for underscore version
                plugin_path = Path(temp_dir) / "test_ollama_plugin".replace("-", "_")

            assert plugin_path.exists(), f"Plugin directory not created at {plugin_path}"

    def test_cli_fallback_generation(self) -> None:
        """Test CLI fallback to template generation when no LLM providers available."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self._run_cli_command([
                "new", "plugin", "test_fallback_plugin",
                "--output-dir", temp_dir,
                "--template", "basic",
                "--no-ai"  # Explicitly disable AI
            ])

            assert result.returncode == 0

            # Check if plugin directory was created
            plugin_path = Path(temp_dir) / "test_fallback_plugin"
            if not plugin_path.exists():
                # Check for underscore version
                plugin_path = Path(temp_dir) / "test_fallback_plugin".replace("-", "_")

            assert plugin_path.exists(), f"Plugin directory not created at {plugin_path}"

            # Check for essential files
            expected_files = ["manifest.yaml", "test_fallback_plugin.py"]
            for file_name in expected_files:
                if file_name.endswith(".py"):
                    file_name = file_name.replace("-", "_")
                file_path = plugin_path / file_name
                assert file_path.exists(), f"Expected file {file_name} not found"

    def test_cli_model_tier_validation(self) -> None:
        """Test CLI validation of model tier parameter."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test invalid model tier
            result = self._run_cli_command([
                "new", "plugin", "test_invalid_tier",
                "--output-dir", temp_dir,
                "--model-tier", "invalid"
            ])

            # Should fail with invalid choice
            assert result.returncode != 0
            assert "invalid choice" in result.stderr.lower()

    def test_cli_provider_validation(self) -> None:
        """Test CLI validation of provider parameter."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test invalid provider
            result = self._run_cli_command([
                "new", "plugin", "test_invalid_provider",
                "--output-dir", temp_dir,
                "--preferred-provider", "invalid"
            ])

            # Should fail with invalid choice
            assert result.returncode != 0
            assert "invalid choice" in result.stderr.lower()

    def test_cli_cost_parameter(self) -> None:
        """Test CLI cost parameter handling."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test with cost parameter (should not fail even without API keys)
            result = self._run_cli_command([
                "new", "plugin", "test_cost_param",
                "--output-dir", temp_dir,
                "--max-cost", "0.001",
                "--no-ai"  # Disable AI to avoid API key requirements
            ])

            # Should succeed with fallback generation
            assert result.returncode == 0

    def test_cli_response_time_parameter(self) -> None:
        """Test CLI response time parameter handling."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test with response time parameter
            result = self._run_cli_command([
                "new", "plugin", "test_time_param",
                "--output-dir", temp_dir,
                "--max-response-time", "1000",
                "--no-ai"  # Disable AI to avoid API key requirements
            ])

            # Should succeed with fallback generation
            assert result.returncode == 0
