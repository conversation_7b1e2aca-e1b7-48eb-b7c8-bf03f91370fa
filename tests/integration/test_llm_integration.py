"""
Integration tests for LLM providers with real API calls.

These tests verify that all LLM providers work correctly with real APIs.
They require environment variables for API keys.
"""

import json
import os

import pytest

from plugginger.plugins.core.llm_provider.services.provider_service import ProviderFactory


class TestLLMProviderIntegration:
    """Integration tests for LLM providers with real API calls."""

    @pytest.mark.asyncio
    @pytest.mark.skipif(
        not os.getenv("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY", "").startswith("sk-test-"),
        reason="Real OPENAI_API_KEY not set (test keys not supported)"
    )
    async def test_openai_text_generation(self) -> None:
        """Test OpenAI text generation with real API."""
        provider = ProviderFactory.create_provider(
            "openai",
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-4o-mini"
        )

        result = await provider.generate_text(
            prompt="Say hello in exactly 3 words",
            max_tokens=10,
            temperature=0.1
        )

        assert result["success"] is True
        assert len(result["content"]) > 0
        assert result["provider"] == "openai"
        # OpenAI may return versioned model names like 'gpt-4o-mini-2024-07-18'
        assert result["model"].startswith("gpt-4o-mini")
        assert result["tokens_used"] > 0

    @pytest.mark.asyncio
    @pytest.mark.skipif(
        not os.getenv("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY", "").startswith("sk-test-"),
        reason="Real OPENAI_API_KEY not set (test keys not supported)"
    )
    async def test_openai_structured_generation(self) -> None:
        """Test OpenAI structured JSON generation with real API."""
        provider = ProviderFactory.create_provider(
            "openai",
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-4o-mini"
        )

        result = await provider.generate_structured(
            system_message="You are a helpful assistant that responds in JSON format.",
            user_message="Create a plugin spec with name and description for a hello world plugin.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )

        assert result["success"] is True
        assert result["validated"] is True
        assert len(result["content"]) > 0

        # Validate JSON structure
        parsed = json.loads(result["content"])
        assert "name" in parsed
        assert "description" in parsed
        assert isinstance(parsed["name"], str)
        assert isinstance(parsed["description"], str)

    @pytest.mark.asyncio
    @pytest.mark.skipif(not os.getenv("GOOGLE_API_KEY"), reason="GOOGLE_API_KEY not set")
    async def test_gemini_text_generation(self) -> None:
        """Test Gemini text generation with real API."""
        provider = ProviderFactory.create_provider(
            "gemini",
            api_key=os.getenv("GOOGLE_API_KEY"),
            model="gemini-1.5-flash"
        )

        result = await provider.generate_text(
            prompt="Say hello in exactly 3 words",
            max_tokens=10,
            temperature=0.1
        )

        assert result["success"] is True
        assert len(result["content"]) > 0
        assert result["provider"] == "gemini"
        assert result["model"] == "gemini-1.5-flash"
        assert result["tokens_used"] > 0

    @pytest.mark.asyncio
    @pytest.mark.skipif(not os.getenv("GOOGLE_API_KEY"), reason="GOOGLE_API_KEY not set")
    async def test_gemini_structured_generation(self) -> None:
        """Test Gemini structured JSON generation with real API."""
        provider = ProviderFactory.create_provider(
            "gemini",
            api_key=os.getenv("GOOGLE_API_KEY"),
            model="gemini-1.5-flash"
        )

        result = await provider.generate_structured(
            system_message="You are a helpful assistant that responds in JSON format.",
            user_message="Create a plugin spec with name and description for a hello world plugin.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )

        assert result["success"] is True
        assert result["validated"] is True
        assert len(result["content"]) > 0

        # Validate JSON structure
        parsed = json.loads(result["content"])
        assert "name" in parsed
        assert "description" in parsed

    @pytest.mark.asyncio
    async def test_ollama_text_generation(self) -> None:
        """Test Ollama text generation with real API."""
        # Check if Ollama is available
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:11434/api/tags", timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status != 200:
                        pytest.skip("Ollama not available")
        except Exception:
            pytest.skip("Ollama not available")

        provider = ProviderFactory.create_provider(
            "ollama",
            model="granite3-dense:2b"
        )

        result = await provider.generate_text(
            prompt="Say hello in exactly 3 words",
            max_tokens=10,
            temperature=0.1
        )

        assert result["success"] is True
        assert len(result["content"]) > 0
        assert result["provider"] == "ollama"
        assert result["model"] == "granite3-dense:2b"

    @pytest.mark.asyncio
    async def test_ollama_structured_generation(self) -> None:
        """Test Ollama structured JSON generation with real API."""
        # Check if Ollama is available
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:11434/api/tags", timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status != 200:
                        pytest.skip("Ollama not available")
        except Exception:
            pytest.skip("Ollama not available")

        provider = ProviderFactory.create_provider(
            "ollama",
            model="granite3-dense:2b"
        )

        result = await provider.generate_structured(
            system_message="You are a helpful assistant that responds in JSON format.",
            user_message="Create a plugin spec with name and description for a hello world plugin.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=3,  # Ollama might need more retries
            temperature=0.1
        )

        assert result["success"] is True
        assert result["validated"] is True
        assert len(result["content"]) > 0

        # Validate JSON structure
        parsed = json.loads(result["content"])
        assert "name" in parsed
        assert "description" in parsed

    @pytest.mark.asyncio
    async def test_provider_factory_auto_detection(self) -> None:
        """Test provider factory auto-detection based on environment variables."""
        # Test with OPENAI_API_KEY (skip test keys)
        if os.getenv("OPENAI_API_KEY") and not os.getenv("OPENAI_API_KEY", "").startswith("sk-test-"):
            provider = ProviderFactory.create_from_env()
            assert provider.provider_name == "openai"
            assert provider.api_key == os.getenv("OPENAI_API_KEY")

        # Test with GOOGLE_API_KEY
        elif os.getenv("GOOGLE_API_KEY"):
            provider = ProviderFactory.create_from_env()
            assert provider.provider_name == "gemini"
            assert provider.api_key == os.getenv("GOOGLE_API_KEY")

        else:
            # No API keys available, should raise error
            with pytest.raises(ValueError, match="No LLM provider configured"):
                ProviderFactory.create_from_env()

    @pytest.mark.asyncio
    async def test_provider_performance_comparison(self) -> None:
        """Compare performance of different providers."""
        import time

        prompt = "Hello"
        results = {}

        # Test Ollama (if available)
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:11434/api/tags", timeout=aiohttp.ClientTimeout(total=2)) as response:
                    if response.status == 200:
                        provider = ProviderFactory.create_provider("ollama", model="granite3-dense:2b")
                        start_time = time.time()
                        result = await provider.generate_text(prompt, max_tokens=5)
                        end_time = time.time()
                        if result["success"]:
                            results["ollama"] = end_time - start_time
        except Exception:
            pass

        # Test Gemini (if available)
        if os.getenv("GOOGLE_API_KEY"):
            provider = ProviderFactory.create_provider("gemini", api_key=os.getenv("GOOGLE_API_KEY"))
            start_time = time.time()
            result = await provider.generate_text(prompt, max_tokens=5)
            end_time = time.time()
            if result["success"]:
                results["gemini"] = end_time - start_time

        # Test OpenAI (if available, skip test keys)
        if os.getenv("OPENAI_API_KEY") and not os.getenv("OPENAI_API_KEY", "").startswith("sk-test-"):
            provider = ProviderFactory.create_provider("openai", api_key=os.getenv("OPENAI_API_KEY"))
            start_time = time.time()
            result = await provider.generate_text(prompt, max_tokens=5)
            end_time = time.time()
            if result["success"]:
                results["openai"] = end_time - start_time

        # At least one provider should be available
        assert len(results) > 0, "No LLM providers available for testing"

        # Print performance results
        print("\nProvider Performance Results:")
        for provider_name, duration in sorted(results.items(), key=lambda item: item[1]):
            print(f"  {provider_name}: {duration:.3f}s")

    @pytest.mark.asyncio
    async def test_error_handling(self) -> None:
        """Test error handling with invalid API keys."""
        # Test with invalid OpenAI key
        provider = ProviderFactory.create_provider("openai", api_key="invalid-key")
        result = await provider.generate_text("Hello")

        assert result["success"] is False
        assert "error" in result
        assert result["tokens_used"] == 0

        # Test with invalid Gemini key
        provider = ProviderFactory.create_provider("gemini", api_key="invalid-key")
        result = await provider.generate_text("Hello")

        assert result["success"] is False
        assert "error" in result
        assert result["tokens_used"] == 0
