"""
Unit tests for LLM provider mock responses when libraries are not available.

These tests verify that the providers handle missing dependencies correctly
and return appropriate error responses instead of mock success responses.
"""

import pytest

from plugginger.plugins.core.llm_provider.services.provider_service import (
    OpenAIProvider,
    GeminiProvider,
    OllamaProvider,
)


class TestLLMProviderMockResponses:
    """Test LLM provider behavior when dependencies are missing."""

    @pytest.mark.asyncio
    async def test_openai_provider_missing_library_error_handling(self) -> None:
        """Test OpenAI provider returns error when library is missing."""
        provider = OpenAIProvider(api_key="test-key", model="gpt-4o-mini")
        
        # Test text generation
        result = await provider.generate_text("Hello")
        
        assert result["success"] is False
        assert "error" in result
        assert "OpenAI library not available" in result["error"]
        assert result["tokens_used"] == 0
        assert result["provider"] == "openai"
        assert result["model"] == "gpt-4o-mini"

    @pytest.mark.asyncio
    async def test_openai_provider_missing_library_structured_generation(self) -> None:
        """Test OpenAI provider structured generation returns error when library is missing."""
        provider = OpenAIProvider(api_key="test-key", model="gpt-4o-mini")
        
        # Test structured generation
        result = await provider.generate_structured(
            system_message="You are a helpful assistant.",
            user_message="Create a plugin spec with name and description.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )
        
        assert result["success"] is False
        assert result["validated"] is False
        assert "error" in result
        assert "OpenAI library not available" in result["error"]
        assert result["tokens_used"] == 0
        assert result["retries_used"] == 0
        assert result["provider"] == "openai"
        assert result["model"] == "gpt-4o-mini"

    @pytest.mark.asyncio
    async def test_gemini_provider_invalid_api_key_error_handling(self) -> None:
        """Test Gemini provider returns error with invalid API key."""
        provider = GeminiProvider(api_key="invalid-key", model="gemini-1.5-flash")
        
        # Test text generation with invalid key
        result = await provider.generate_text("Hello")
        
        assert result["success"] is False
        assert "error" in result
        assert result["tokens_used"] == 0
        assert result["provider"] == "gemini"
        assert result["model"] == "gemini-1.5-flash"

    @pytest.mark.asyncio
    async def test_gemini_provider_invalid_api_key_structured_generation(self) -> None:
        """Test Gemini provider structured generation returns error with invalid API key."""
        provider = GeminiProvider(api_key="invalid-key", model="gemini-1.5-flash")
        
        # Test structured generation with invalid key
        result = await provider.generate_structured(
            system_message="You are a helpful assistant.",
            user_message="Create a plugin spec with name and description.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )
        
        assert result["success"] is False
        assert result["validated"] is False
        assert "error" in result
        assert result["tokens_used"] == 0
        assert result["retries_used"] == 0
        assert result["provider"] == "gemini"
        assert result["model"] == "gemini-1.5-flash"

    @pytest.mark.asyncio
    async def test_ollama_provider_missing_aiohttp_error_handling(self) -> None:
        """Test Ollama provider returns error when aiohttp is missing."""
        provider = OllamaProvider(api_key="", model="llama3.2")
        
        # Test structured generation (which has the aiohttp import check)
        result = await provider.generate_structured(
            system_message="You are a helpful assistant.",
            user_message="Create a plugin spec with name and description.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )
        
        # Note: This test might pass if aiohttp is available, but if it's missing,
        # it should return an error response
        if not result["success"]:
            assert result["validated"] is False
            assert "error" in result
            assert result["tokens_used"] == 0
            assert result["retries_used"] == 0
            assert result["provider"] == "ollama"
            assert result["model"] == "llama3.2"
