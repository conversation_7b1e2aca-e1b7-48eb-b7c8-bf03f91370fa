"""
Unit tests for LLM provider error handling and mock responses.

These tests verify that the providers handle various error conditions correctly,
including missing dependencies, invalid API keys, and network errors.
"""

import os
from unittest.mock import patch

import pytest

from plugginger.plugins.core.llm_provider.services.provider_service import (
    GeminiProvider,
    OllamaProvider,
    OpenAIProvider,
)


class TestLLMProviderMockResponses:
    """Test LLM provider behavior in various error conditions."""

    @pytest.mark.asyncio
    @patch('plugginger.plugins.core.llm_provider.services.provider_service.OPENAI_AVAILABLE', False)
    async def test_openai_provider_missing_library_error_handling(self) -> None:
        """Test OpenAI provider returns error when library is missing."""
        # Use environment variable or fallback to test key
        api_key = os.getenv("OPENAI_API_KEY", "test-key")
        provider = OpenAIProvider(api_key=api_key, model="gpt-4.1")

        # Test text generation
        result = await provider.generate_text("Hello")

        assert result["success"] is False
        assert "error" in result
        assert "OpenAI library not available" in result["error"]
        assert result["tokens_used"] == 0
        assert result["provider"] == "openai"
        assert result["model"] == "gpt-4.1"

    @pytest.mark.asyncio
    @patch('plugginger.plugins.core.llm_provider.services.provider_service.OPENAI_AVAILABLE', False)
    async def test_openai_provider_missing_library_structured_generation(self) -> None:
        """Test OpenAI provider structured generation returns error when library is missing."""
        # Use environment variable or fallback to test key
        api_key = os.getenv("OPENAI_API_KEY", "test-key")
        provider = OpenAIProvider(api_key=api_key, model="gpt-4.1")

        # Test structured generation
        result = await provider.generate_structured(
            system_message="You are a helpful assistant.",
            user_message="Create a plugin spec with name and description.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )

        assert result["success"] is False
        assert result["validated"] is False
        assert "error" in result
        assert "OpenAI library not available" in result["error"]
        assert result["tokens_used"] == 0
        assert result["retries_used"] == 0
        assert result["provider"] == "openai"
        assert result["model"] == "gpt-4.1"

    @pytest.mark.asyncio
    async def test_gemini_provider_invalid_api_key_error_handling(self) -> None:
        """Test Gemini provider returns error with invalid API key."""
        # Use explicitly invalid key for testing
        provider = GeminiProvider(api_key="invalid-key", model="gemini-2.0-flash-exp")

        # Test text generation with invalid key
        result = await provider.generate_text("Hello")

        assert result["success"] is False
        assert "error" in result
        assert result["tokens_used"] == 0
        assert result["provider"] == "gemini"
        assert result["model"] == "gemini-2.0-flash-exp"

    @pytest.mark.asyncio
    async def test_gemini_provider_invalid_api_key_structured_generation(self) -> None:
        """Test Gemini provider structured generation returns error with invalid API key."""
        # Use explicitly invalid key for testing
        provider = GeminiProvider(api_key="invalid-key", model="gemini-2.0-flash-exp")

        # Test structured generation with invalid key
        result = await provider.generate_structured(
            system_message="You are a helpful assistant.",
            user_message="Create a plugin spec with name and description.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )

        assert result["success"] is False
        assert result["validated"] is False
        assert "error" in result
        assert result["tokens_used"] == 0
        assert result["retries_used"] >= 0  # May retry with invalid API key
        assert result["provider"] == "gemini"
        assert result["model"] == "gemini-2.0-flash-exp"

    @pytest.mark.asyncio
    async def test_ollama_provider_missing_aiohttp_error_handling(self) -> None:
        """Test Ollama provider returns error when aiohttp is missing."""
        provider = OllamaProvider(api_key="", model="qwen2.5-coder:7b")

        # Test structured generation (which has the aiohttp import check)
        result = await provider.generate_structured(
            system_message="You are a helpful assistant.",
            user_message="Create a plugin spec with name and description.",
            ebnf_grammar='{"name": string, "description": string}',
            max_retries=2,
            temperature=0.1
        )

        # Note: This test might pass if aiohttp is available, but if it's missing,
        # it should return an error response
        if not result["success"]:
            assert result["validated"] is False
            assert "error" in result
            assert result["tokens_used"] == 0
            assert result["retries_used"] == 0
            assert result["provider"] == "ollama"
            assert result["model"] == "qwen2.5-coder:7b"
